"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Copy } from "lucide-react";
import { toast } from "sonner";
import { Veo3JsonGeneratorConfig, Veo3Template, Veo3FormData } from "@/types/veo-3";
import { veo3Templates } from "@/lib/veo-3-templates";
import DynamicForm from "./veo-3-dynamic-form";
import JsonPreview from "./veo-3-json-preview";
import VideoPlayer from "./veo-3-video-player";
import { useParams } from "next/navigation";

interface TemplateModeProps {
  config: Veo3JsonGeneratorConfig;
}

export default function TemplateMode({ config }: TemplateModeProps) {
  const params = useParams();
  const locale = (params.locale as string) || "en";
  const [selectedTemplate, setSelectedTemplate] = useState<Veo3Template | null>(null);
  const [formData, setFormData] = useState<Veo3FormData>({});
  const [jsonOutput, setJsonOutput] = useState<string>("");

  // Helper function to get localized text
  const getLocalizedText = (textObj: { en: string; zh?: string }) => {
    if (locale === "zh" && textObj.zh) {
      return textObj.zh;
    }
    return textObj.en;
  };

  // 初始化选择第一个模板
  useEffect(() => {
    if (veo3Templates.length > 0) {
      const firstTemplate = veo3Templates[0];
      setSelectedTemplate(firstTemplate);
      setFormData(firstTemplate.defaultValues);
    }
  }, []);

  // 当表单数据变化时更新JSON输出
  useEffect(() => {
    if (selectedTemplate && formData) {
      try {
        const jsonString = JSON.stringify(formData, null, 2);
        setJsonOutput(jsonString);
      } catch (error) {
        console.error("Error generating JSON:", error);
      }
    }
  }, [formData, selectedTemplate]);

  const handleTemplateChange = (templateId: string) => {
    const template = veo3Templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      setFormData(template.defaultValues);
    }
  };

  const handleFormDataChange = (newData: Veo3FormData) => {
    setFormData(newData);
  };

  const handleCopyJson = async () => {
    if (!jsonOutput) {
      toast.error(config.errorCopyFailed);
      return;
    }

    try {
      await navigator.clipboard.writeText(jsonOutput);
      toast.success(config.successCopied);
    } catch (error) {
      toast.error(config.errorCopyFailed);
    }
  };

  return (
    <div className="space-y-6">
      {selectedTemplate && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左侧：动态表单 */}
          <div className="space-y-6">
            <Card>
              <CardContent className="space-y-6">
                {/* Select Template 区域 */}
                <div className="flex items-center gap-4">
                  <h3 className="text-lg font-semibold whitespace-nowrap text-card-foreground">{config.selectTemplate}</h3>
                  <Select value={selectedTemplate?.id || ""} onValueChange={handleTemplateChange}>
                    <SelectTrigger className="flex-1 bg-input border-border text-foreground">
                      <SelectValue placeholder={locale === "zh" ? "选择一个模板..." : "Choose a template..."} />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      {veo3Templates.map((template) => (
                        <SelectItem key={template.id} value={template.id} className="text-popover-foreground">
                          <span className="font-medium">
                            {getLocalizedText(template.name)}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Template Fields 区域 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-card-foreground">Template Fields</h3>
                  <DynamicForm
                    template={selectedTemplate}
                    formData={formData}
                    onChange={handleFormDataChange}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：JSON预览 */}
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>{config.jsonPreview}</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyJson}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  {config.copyJson}
                </Button>
              </CardHeader>
              <CardContent>
                <JsonPreview content={jsonOutput} />
              </CardContent>
            </Card>

            {/* 样例视频 */}
            {selectedTemplate.sampleVideo && (
              <Card>
                <CardHeader>
                  <CardTitle>{config.sampleVideo}</CardTitle>
                </CardHeader>
                <CardContent>
                  <VideoPlayer
                    coverImage={selectedTemplate.sampleVideo.coverImage}
                    videoUrl={selectedTemplate.sampleVideo.videoUrl}
                    title={getLocalizedText(selectedTemplate.sampleVideo.title)}
                  />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
